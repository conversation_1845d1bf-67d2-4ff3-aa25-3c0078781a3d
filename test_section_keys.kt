// Test to demonstrate the fix for duplicate section keys
import com.example.habits9.data.HabitSection

fun main() {
    // Before the fix: Multiple sections would have id = 0, causing duplicate keys
    // After the fix: Each section has a unique string ID from Firestore
    
    val section1 = HabitSection(
        id = "firestore_doc_id_abc123",
        name = "Work",
        color = 0xFF0000FF.toInt(),
        displayOrder = 0
    )
    
    val section2 = HabitSection(
        id = "firestore_doc_id_def456", 
        name = "Personal",
        color = 0xFF00FF00.toInt(),
        displayOrder = 1
    )
    
    val section3 = HabitSection(
        id = "firestore_doc_id_ghi789",
        name = "Health", 
        color = 0xFFFF0000.toInt(),
        displayOrder = 2
    )
    
    val sections = listOf(section1, section2, section3)
    
    // Generate keys as they would be in LazyColumn
    val keys = sections.map { "section_${it.id}" }
    
    println("Section keys:")
    keys.forEach { println("  $it") }
    
    // Check for duplicates
    val uniqueKeys = keys.toSet()
    println("\nUnique keys count: ${uniqueKeys.size}")
    println("Total keys count: ${keys.size}")
    println("Has duplicates: ${uniqueKeys.size != keys.size}")
    
    // This should now show no duplicates, fixing the LazyColumn crash
}
