package com.example.habits9.data.firestore

import com.example.habits9.data.Habit
import com.example.habits9.data.Completion
import com.example.habits9.data.HabitSection
import kotlin.math.abs

/**
 * Utility functions to convert between Room entities and Firestore models.
 */
object FirestoreConverters {

    /**
     * Converts a Room Habit entity to a Firestore model.
     */
    fun habitToFirestore(habit: Habit): FirestoreHabit {
        return FirestoreHabit(
            id = if (habit.id == 0L) "" else habit.id.toString(), // Convert Long ID to String for Firestore, empty for new habits
            name = habit.name,
            description = habit.description,
            creationDate = habit.creationDate,
            createdAt = habit.creationDate, // Map creationDate to createdAt for compatibility
            currentStreak = habit.currentStreak,
            completionDatesJson = habit.completionDatesJson,
            uuid = habit.uuid,
            isArchived = habit.isArchived,
            position = habit.position,
            color = habit.color,
            type = habit.type,
            targetType = habit.targetType,
            targetValue = habit.targetValue,
            unit = habit.unit,
            frequencyType = habit.frequencyType,
            repeatsEvery = habit.repeatsEvery,
            daysOfWeek = habit.daysOfWeek,
            dayOfMonth = habit.dayOfMonth,
            weekOfMonth = habit.weekOfMonth,
            dayOfWeekInMonth = habit.dayOfWeekInMonth
        )
    }
    
    /**
     * Converts a Firestore model to a Room Habit entity.
     */
    fun firestoreToHabit(firestoreHabit: FirestoreHabit): Habit {
        // Use createdAt if available and not 0, otherwise fall back to creationDate
        val creationDate = if (firestoreHabit.createdAt != 0L) firestoreHabit.createdAt else firestoreHabit.creationDate

        return Habit(
            // FIXED: Use hash code of Firestore document ID to generate unique Long IDs
            // This ensures each habit has a unique ID while maintaining consistency
            id = firestoreHabit.id.hashCode().toLong().let { if (it == 0L) 1L else kotlin.math.abs(it) },
            name = firestoreHabit.name,
            description = firestoreHabit.description,
            creationDate = creationDate,
            currentStreak = firestoreHabit.currentStreak,
            completionDatesJson = firestoreHabit.completionDatesJson,
            uuid = firestoreHabit.uuid,
            isArchived = firestoreHabit.isArchived,
            position = firestoreHabit.position,
            color = firestoreHabit.color,
            type = firestoreHabit.type,
            targetType = firestoreHabit.targetType,
            targetValue = firestoreHabit.targetValue,
            unit = firestoreHabit.unit,
            frequencyType = firestoreHabit.frequencyType,
            repeatsEvery = firestoreHabit.repeatsEvery,
            daysOfWeek = firestoreHabit.daysOfWeek,
            dayOfMonth = firestoreHabit.dayOfMonth,
            weekOfMonth = firestoreHabit.weekOfMonth,
            dayOfWeekInMonth = firestoreHabit.dayOfWeekInMonth
        )
    }
    
    /**
     * Converts a Room Completion entity to a Firestore model.
     */
    fun completionToFirestore(completion: Completion, habitDocumentId: String): FirestoreCompletion {
        return FirestoreCompletion(
            id = completion.id, // Keep the Firestore document ID as string, empty for new completions
            habitId = habitDocumentId, // Use Firestore document ID instead of Room ID
            timestamp = completion.timestamp,
            value = completion.value
        )
    }
    
    /**
     * Converts a Firestore model to a Room Completion entity.
     */
    fun firestoreToCompletion(firestoreCompletion: FirestoreCompletion, roomHabitId: Long): Completion {
        return Completion(
            id = firestoreCompletion.id, // Keep the Firestore document ID as string for proper deletion
            habitId = roomHabitId, // Use Room habit ID for local compatibility
            timestamp = firestoreCompletion.timestamp,
            value = firestoreCompletion.value
        )
    }
    
    /**
     * Converts a HabitSection entity to a Firestore model.
     */
    fun habitSectionToFirestore(habitSection: HabitSection): FirestoreHabitSection {
        return FirestoreHabitSection(
            id = habitSection.id, // Use string ID directly
            name = habitSection.name,
            color = habitSection.color,
            displayOrder = habitSection.displayOrder
        )
    }

    /**
     * Converts a Firestore model to a HabitSection entity.
     */
    fun firestoreToHabitSection(firestoreHabitSection: FirestoreHabitSection): HabitSection {
        return HabitSection(
            id = firestoreHabitSection.id, // Use string ID directly
            name = firestoreHabitSection.name,
            color = firestoreHabitSection.color,
            displayOrder = firestoreHabitSection.displayOrder
        )
    }
}
